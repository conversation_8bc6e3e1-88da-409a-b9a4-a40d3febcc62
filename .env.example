#env variable to use
NODE_ENV = DEVELOPMENT

#Database to use
DB = MONGO

#HTTP layer to use
API = REST

#MONGODB
MONGODB_URI = mongodb://localhost:27017/fitsomnia-dev

#application
PORT = 3000 
HOST = localhost
REST_API_PREFIX = api

#nodemailer
NODEMAILER_HOST = smtp.example.com
NODEMAILER_USER = secret_email
NODEMAILER_PASS = secret
NODEMAILER_PORT = 465
NODEMAILER_SERVICE = gmail
NODEMAILER_SECURE = true

#swagger
SWAGGER_TITLE = FITSOMNIA
SWAGGER_DESCRIPTION = FITSOMNIA API
SWAGGER_VERSION = 1.0
SWAGGER_AUTH_TYPE = http
SWAGGER_AUTH_DESCRIPTION = JWT Token
SWAGGER_AUTH_SCHEMA = bearer
SWAGGER_AUTH_NAME = Authorization
SWAGGER_AUTH_BEARER_FORMAT = JWT
SWAGGER_API_PREFIX = api

#Authentication
JWT_SALT = 10
JWT_SECRET_KEY = @ADFIT23@!46
JWT_EXPIRATION_TIME = 24h

#signup
SIGNUP_OTP_SUBJECT = Create Account OTP
SIGNUP_OTP_BODY_PREFIX = Your FITSOMNIA OTP is
SIGNUP_OTP_EXPIRATION_TIME = 180000           # 3 * 60 * 1000 (3 minutes)

#forgot password
FORGOT_PASSWORD_OTP_SUBJECT = Forgot Password OTP
FORGOT_PASSWORD_OTP_BODY_PREFIX = Your FITSOMNIA OTP is
FORGOT_PASSWORD_OTP_EXPIRATION_TIME = 180000
SET_NEW_PASSWORD_EXPIRATION_TIME = 300000        # 5 * 60 * 1000 (5 minutes)

#change email
CHANGE_EMAIL_OTP_SUBJECT = Change Email OTP
CHANGE_EMAIL_OTP_BODY_PREFIX = Your FITSOMNIA OTP is
CHANGE_EMAIL_OTP_EXPIRATION_TIME = 180000           # 3 * 60 * 1000 (3 minutes)

#phone
PHONE_NUMBER_REGION = BD

#url
AUTH_RESET_ORIGINAL_URL = /auth/reset/
BASE_URL = http://localhost:3000

#multer
MULTER_FILE_DESTINATION = src/public/assets
MULTER_FILE_SIZE = 3 * 1024 * 1024
MULTER_FILE_EXTENSIONS = ['image/png', 'image/jpeg', 'image/svg+xml',  'image/jpg', 'image/gif']

#social-signin
SOCIAL_DEFAULT_PASSWORD_POSTFIX = password61@
GOOGLE_USER_INFO_API_URL = https://www.googleapis.com/oauth2/v3/userinfo
FACEBOOK_USER_INFO_API_URL = https://graph.facebook.com/me
APPLE_CLIENT_ID = com.fitsomnia.fitsomniaApp
APPLE_WEB_CLIENT_ID = com.fitsomnia.fitsomniaApp-service
APPLE_ANDROID_PACKAGE_IDENTIFIER = com.fitsomnia.fitsomnia_app

# my-club
CLUB_DESCRIPTION_LENGTH = 450
CLUB_MEMBER_EXPIRED_DATE = 30

# aws s3
AWS_S3_APIVERSION = 
AWS_S3_BUCKETNAME = 
AWS_ACCESSKEYID = 
AWS_SECRETACCESSKEY = 
AWS_REGION = 
AWS_PRESIGNED_EXPIRE_TIME = 60

# Azure Blob Storage
AZURE_STORAGE_CONNECTION_STRING = 
AZURE_STORAGE_ACCOUNT_NAME = 
AZURE_STORAGE_ACCOUNT_KEY = 
AZURE_STORAGE_CONTAINER_NAME = 
AZURE_STORAGE_PUBLIC_CONTAINER_NAME = 
AZURE_PRESIGNED_EXPIRE_TIME = 60
AZURE_STORAGE_CDN_URL = 
AZURE_STORAGE_PUBLIC_CDN_URL = 
AZURE_STORAGE_PUBLIC_URL = 

# story config
STORY_EXPIRATION_TIME=********

#redis
REDIS_CACHE_USERNAME = 
REDIS_CACHE_PASSWORD = 
REDIS_CACHE_HOST_NAME = 127.0.0.1
REDIS_CACHE_HOST_PORT = 6379

# Vertex AI
VERTEX_AI_PROJECT_ID = test-bot-437506
VERTEX_AI_LOCATION = asia-southeast1