import { Test, TestingModule } from '@nestjs/testing';
import { HttpStatus } from '@nestjs/common';
import { UserAuthService } from 'src/modules/user-auth/services';
import { UserRepository } from 'src/modules/user/repositories';
import { Helper } from 'src/helper/helper.interface';
import { JwtService } from '@nestjs/jwt';
import { ESearchDbService } from 'src/modules/global-search/services/elastic.db.service';
import { UserAuthRepository } from 'src/modules/user-auth/repositories';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import { APIException } from 'src/internal/exception/api.exception';
import { AppleSignInErrorMessages } from 'models';
import Redis from 'ioredis';

describe('UserAuthService - Apple Sign In', () => {
  let service: UserAuthService;
  let userRepo: jest.Mocked<UserRepository>;
  let helper: jest.Mocked<Helper>;
  let jwtService: jest.Mocked<JwtService>;
  let eSearchDbService: jest.Mocked<ESearchDbService>;
  let userAuthRepository: jest.Mocked<UserAuthRepository>;
  let notificationHelperService: jest.Mocked<NotificationHelperService>;
  let redis: any;

  beforeEach(async () => {
    const mockUserRepo = {
      findUser: jest.fn(),
      createUser: jest.fn(),
      updateUser: jest.fn(),
    };

    const mockHelper = {
      serviceResponse: {
        successResponse: jest.fn().mockImplementation((data) => ({ data })),
      },
    };

    const mockJwtService = {
      sign: jest.fn(),
    };

    const mockESearchDbService = {
      insertOneItemToES: jest.fn(),
    };

    const mockUserAuthRepository = {
      getUserSession: jest.fn(),
      createUserSession: jest.fn(),
      updateUserSession: jest.fn(),
    };

    const mockNotificationHelperService = {
      subscribeUserToReminderTopic: jest.fn(),
    };

    const mockRedis = {
      set: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserAuthService,
        { provide: UserRepository, useValue: mockUserRepo },
        { provide: Helper, useValue: mockHelper },
        { provide: JwtService, useValue: mockJwtService },
        { provide: ESearchDbService, useValue: mockESearchDbService },
        { provide: UserAuthRepository, useValue: mockUserAuthRepository },
        { provide: NotificationHelperService, useValue: mockNotificationHelperService },
        { provide: 'default_IORedisModuleConnectionToken', useValue: mockRedis },
      ],
    }).compile();

    service = module.get<UserAuthService>(UserAuthService);
    userRepo = module.get(UserRepository);
    helper = module.get(Helper);
    jwtService = module.get(JwtService);
    eSearchDbService = module.get(ESearchDbService);
    userAuthRepository = module.get(UserAuthRepository);
    notificationHelperService = module.get(NotificationHelperService);
    redis = module.get('default_IORedisModuleConnectionToken');
  });

  describe('getAppleClientId', () => {
    it('should return iOS client ID for platform "ios"', () => {
      // Access the private method through any casting
      const clientId = (service as any).getAppleClientId('ios');
      expect(clientId).toBe('com.fitsomnia.fitsomnia'); // Default iOS client ID
    });

    it('should return iOS client ID for undefined platform (legacy support)', () => {
      const clientId = (service as any).getAppleClientId(undefined);
      expect(clientId).toBe('com.fitsomnia.fitsomnia'); // Default iOS client ID
    });

    it('should return web client ID for platform "web"', () => {
      const clientId = (service as any).getAppleClientId('web');
      expect(clientId).toBe('com.fitsomnia.fitsomnia.web'); // Default web client ID
    });

    it('should throw error for web platform when web client ID is not configured', () => {
      // Mock environment where web client ID is not set
      const originalConfig = require('config/auth').authConfig;
      const mockConfig = { ...originalConfig };
      mockConfig.apple.webClientId = '';
      
      jest.doMock('config/auth', () => ({ authConfig: mockConfig }));
      
      expect(() => {
        (service as any).getAppleClientId('web');
      }).toThrow(APIException);
    });
  });

  describe('Apple Sign In Platform Support', () => {
    beforeEach(() => {
      // Mock successful token verification
      jest.spyOn(service as any, 'verifyToken').mockResolvedValue({
        email: '<EMAIL>',
        sub: 'apple_user_id',
      });

      // Mock session creation
      userAuthRepository.createUserSession.mockResolvedValue({
        userId: 'user-id',
        sessionId: 'test-session-id',
        expireAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      jwtService.sign.mockReturnValue('test-jwt-token');
    });

    it('should handle Apple Sign In without platform (legacy support)', async () => {
      userRepo.findUser.mockResolvedValue(null);
      userRepo.createUser.mockResolvedValue({
        id: 'user-id',
        email: '<EMAIL>',
        name: 'Test User',
      } as any);

      const result = await service.appleSignIn({
        idToken: 'test-token',
        name: 'Test User',
        fcmToken: 'fcm-token',
      });

      expect(result.data.token).toBe('test-jwt-token');
      expect(notificationHelperService.subscribeUserToReminderTopic).toHaveBeenCalledWith('fcm-token');
    });

    it('should handle Apple Sign In with iOS platform', async () => {
      userRepo.findUser.mockResolvedValue(null);
      userRepo.createUser.mockResolvedValue({
        id: 'user-id',
        email: '<EMAIL>',
        name: 'Test User',
      } as any);

      const result = await service.appleSignIn({
        idToken: 'test-token',
        name: 'Test User',
        fcmToken: 'fcm-token',
        platform: 'ios',
      });

      expect(result.data.token).toBe('test-jwt-token');
      expect(notificationHelperService.subscribeUserToReminderTopic).toHaveBeenCalledWith('fcm-token');
    });

    it('should handle Apple Sign In with web platform', async () => {
      userRepo.findUser.mockResolvedValue(null);
      userRepo.createUser.mockResolvedValue({
        id: 'user-id',
        email: '<EMAIL>',
        name: 'Test User',
      } as any);

      const result = await service.appleSignIn({
        idToken: 'test-token',
        name: 'Test User',
        platform: 'web',
      });

      expect(result.data.token).toBe('test-jwt-token');
      // No FCM token for web, so subscription should not be called
      expect(notificationHelperService.subscribeUserToReminderTopic).not.toHaveBeenCalled();
    });

    it('should handle existing user with FCM token update', async () => {
      const existingUser = {
        id: 'existing-user-id',
        email: '<EMAIL>',
        name: 'Existing User',
      };

      userRepo.findUser.mockResolvedValue(existingUser as any);
      userRepo.updateUser.mockResolvedValue(existingUser as any);

      const result = await service.appleSignIn({
        idToken: 'test-token',
        fcmToken: 'new-fcm-token',
        platform: 'ios',
      });

      expect(userRepo.updateUser).toHaveBeenCalledWith('existing-user-id', {
        fcmToken: 'new-fcm-token',
      });
      expect(notificationHelperService.subscribeUserToReminderTopic).toHaveBeenCalledWith('new-fcm-token');
      expect(result.data.token).toBe('test-jwt-token');
    });

    it('should throw error when token verification fails', async () => {
      jest.spyOn(service as any, 'verifyToken').mockResolvedValue(null);

      await expect(
        service.appleSignIn({
          idToken: 'invalid-token',
          platform: 'ios',
        })
      ).rejects.toThrow(APIException);
    });
  });
});
