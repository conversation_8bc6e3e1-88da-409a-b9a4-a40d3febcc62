import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import {
  AccessTypeEnum,
  FeatureAccessEntity,
  SubscriptionEntity,
  SubscriptionStatusEnum
} from 'src/modules/subscription/entities/subscription.entity';
import { FeatureTypeEnum, PlanTypeEnum } from 'src/modules/subscription/plans/common/entities/plans.entity';


// Subscription Schema
const SubscriptionSchema = new Schema<SubscriptionEntity>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      required: true,
      index: true,
    },
    planId: {
      type: String,
      required: true,
    },
    planType: {
      type: String,
      enum: PlanTypeEnum,
      required: true,
    },
    status: {
      type: String,
      enum: SubscriptionStatusEnum,
      default: SubscriptionStatusEnum.PendingPayment,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
    autoRenew: {
      type: Boolean,
      default: false,
    },
    paymentId: {
      type: String,
      required: false,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Feature Access Schema
const FeatureAccessSchema = new Schema<FeatureAccessEntity>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      required: true,
      index: true,
    },
    featureType: {
      type: String,
      enum: FeatureTypeEnum,
      required: true,
    },
    accessType: {
      type: String,
      enum: AccessTypeEnum,
      required: true,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: false,
    },
    usageCount: {
      type: Number,
      default: 0,
    },
    usageLimit: {
      type: Number,
      required: false,
    },
    subscriptionId: {
      type: String,
      required: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

// Indexes for better query performance
SubscriptionSchema.index({ userId: 1, status: 1 });
FeatureAccessSchema.index({ userId: 1, featureType: 1 });
FeatureAccessSchema.index({ userId: 1, featureType: 1, accessType: 1 });
FeatureAccessSchema.index({ subscriptionId: 1 });

// Export models
export const SubscriptionModel = model<SubscriptionEntity>('Subscription', SubscriptionSchema);
export const FeatureAccessModel = model<FeatureAccessEntity>('FeatureAccess', FeatureAccessSchema);
