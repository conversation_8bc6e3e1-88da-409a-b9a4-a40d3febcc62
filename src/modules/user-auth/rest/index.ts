import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Patch,
  Post,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { UserAuthService } from '../services';
import {
  CreateUserDto,
  CreateUserSuccessResponseDto,
  UserForgotPasswordDto,
  UserForgotPasswordSuccessResponseDto,
  UserSignInDto,
  UserSignInSuccessResponseDto,
  SendOtpDto,
  SendOtpForSignupDto,
  SendOtpSuccessResponseDto,
  VerifyOtpDto,
  VerifyOtpSuccessResponseDto,
  AppleSignInDto,
  FacebookSignInDto,
  GoogleUserSignInDto,
  FcmVerifyTokenDto
} from './dto';
import { authConfig } from 'config/auth';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { User as UserInfo } from 'src/decorators/auth.decorator';
import { User } from 'src/entity/user';
@Controller('user-auth')
@ApiTags('Authentication API - User')
export class UserAuthController {
  constructor(private authService: UserAuthService) {}

  @UseGuards(new RolesGuard(['user']))
  @ApiBearerAuth()
  @Get('logout')
  async logout(@UserInfo() user: User) {
    return await this.authService.logout(user.id);
  }

  @Post('signup/send-otp')
  @ApiResponse({
    description: 'Send OTP For User Signup Response',
    type: SendOtpSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Sending OTP for User sign up.' })
  async sendOtp(@Body() data: SendOtpForSignupDto) {
    return await this.authService.signupSendOTP(data);
  }

  @Post('signup/verify-otp')
  @ApiResponse({
    description: 'User Signup Response',
    type: CreateUserSuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  @ApiOperation({ summary: 'Sign up as a user.' })
  async signup(@Body() user: CreateUserDto) {
    return await this.authService.signup(user);
  }

  @Post('sign-in')
  @ApiResponse({
    description: 'User Sign In Response',
    type: UserSignInSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Sign in as a user.' })
  async signIn(@Body() data: UserSignInDto) {
   
    return await this.authService.signIn(data);
  }

  @Post('forgot-password/send-otp')
  @ApiResponse({
    description: 'Send OTP For Forgot Password Response',
    type: SendOtpSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Send an OTP to a User who has forgotten their password.',
  })
  async forgotPasswordSendOTP(@Body() data: SendOtpDto) {
    return await this.authService.forgotPasswordSendOTP(data);
  }

  @Post('forgot-password/verify-otp')
  @ApiResponse({
    description: 'Verify OTP For Forgot Password Response',
    type: VerifyOtpSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Verifying OTP for User forgot password' })
  async forgotPasswordVerifyOtp(@Body() data: VerifyOtpDto) {
    return await this.authService.forgotPasswordVerifyOTP(data);
  }

  @Post('forgot-password')
  @ApiResponse({
    description: 'Forgot Password Response',
    type: UserForgotPasswordSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'User forgot password' })
  async forgotPassword(@Body() data: UserForgotPasswordDto) {
    return await this.authService.forgotPassword(data);
  }

  @UseGuards(new RolesGuard(['user']))
  @ApiBearerAuth()
  @Patch('verify-fcm-token')
  @ApiResponse({
    description: 'Verify fcm token response',
    // type: FcmVerifyTokenDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'User fcm token verify ' })
  async verifyFcmToken(
    @Req() request: Request,
    @Body() data: FcmVerifyTokenDto,
  ) {
    const user: any = await request.user;
    console.log(typeof(data.fcmToken));
    return await this.authService.verifyFcmToken(user.id, data.fcmToken);
  }

  //********** Social SignIn **********

  @Post('google/sign-in')
  @ApiResponse({
    description: 'Google Sign in Response',
    type: UserSignInSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: `Google sign-in. You must provide the google accessToken.
       This accessToken is an opaque string that identifies a user and can be used by the system to make graph API calls.`,
  })
  async googleSignIn(@Body() data: GoogleUserSignInDto) {
    return await this.authService.googleSignIn(data);
  }

  @Post('facebook/sign-in')
  @ApiResponse({
    description: 'Facebook Sign in Response',
    type: UserSignInSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: `Facebook sign-in. You must provide the facebook accessToken.
       This accessToken is an opaque string that identifies a user and can be system by the app to make graph API calls.`,
  })
  async facebookSignIn(@Body() data: FacebookSignInDto) {
    return await this.authService.facebookSignIn(data);
  }

  @Post('apple/sign-in')
  @ApiResponse({
    description: 'Apple Sign in Response',
    type: UserSignInSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: `Apple sign-in. You must provide the apple idToken.
      This idToken is a JSON Web Token that securely communicates information about the user to your app.
      Optionally provide platform ('ios' or 'web') for platform-specific client ID validation.
      If platform is not specified, defaults to 'ios' for backward compatibility.`,
  })
  async appleSignIn(@Body() data: AppleSignInDto) {
    return await this.authService.appleSignIn(data);
  }

  // The bellow callback route used for Android, which will send the callback parameters from Apple into the Android app.
  // This is done using a hyperlink.
  @Get('apple/sign-in/callback')
  @ApiOperation({
    summary: `Apple sign-in callback url.`,
  })
  async appleSignInCallback(
    @Res() response: Response,
    @Req() request: Request,
  ) {
    const redirect = `intent://callback?${new URLSearchParams(
      request?.body,
    ).toString()}#Intent;package=${
      authConfig.apple.packageIdentifier
    };scheme=signinwithapple;end`;

    console.log(`Redirecting to ${redirect}`);
    return response.redirect(307, redirect);
  }
}
