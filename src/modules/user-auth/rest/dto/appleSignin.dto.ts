import { ApiProperty } from '@nestjs/swagger';
import { IsIn, IsObject, IsOptional, IsString } from 'class-validator';
import { AppleSignInRequest, AppleSignInSuccessResponse } from 'models';
import { TokenDto } from './signin.dto';

export class AppleSignInDto implements AppleSignInRequest {
  @ApiProperty()
  @IsString()
  idToken: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  fcmToken?: string;

  @ApiProperty({
    required: false,
    enum: ['ios', 'web'],
    description: 'Platform for Apple Sign-In. Defaults to ios for backward compatibility.'
  })
  @IsOptional()
  @IsIn(['ios', 'web'])
  platform?: 'ios' | 'web';
}

export class AppleSignInSuccessResponseDto
  implements AppleSignInSuccessResponse
{
  @ApiProperty()
  @IsObject()
  data: TokenDto;
}
