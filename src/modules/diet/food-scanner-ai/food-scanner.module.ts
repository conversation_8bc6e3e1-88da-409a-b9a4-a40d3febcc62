import { Module } from '@nestjs/common';
import { AIModule } from 'src/modules/ai/ai.module';
import { SubscriptionModule } from 'src/modules/subscription/subscription.module';
import { FoodScannerController } from './controllers/scanner.controller';
import { ScannerRepository } from './repositories/scanner.repository';
import { FoodScannerService } from './services/scanner.service';

@Module({
  imports: [AIModule, SubscriptionModule],
  controllers: [FoodScannerController],
  providers: [FoodScannerService, ScannerRepository],
  exports: [FoodScannerService]
})
export class FoodScannerModule {}
