import { Module, forwardRef } from '@nestjs/common';
import { AccountingModule } from 'src/modules/accounting/accounting.rest.module';
import { BkashRestModule } from '../bkash/bkash.rest.module';
import { SubscriptionController } from './controllers/subscription.controller';
import { FeaturesModule } from './features/features.rest.module';
import { SubscriptionAccessGuard } from './guards/subscription-access.guard';
import { PlansModule } from './plans/plans.rest.module';
import { SubscriptionProviderForInternal } from './providers/internal.provider';
import { FeatureAccessRepository } from './repositories/feature-access.repository';
import { SubscriptionRepository } from './repositories/subscription.repository';
import { SubscriptionService } from './services/subscription.service';

@Module({
  imports: [forwardRef(() => BkashRestModule), PlansModule, FeaturesModule, forwardRef(() => AccountingModule)],
  controllers: [SubscriptionController],
  providers: [
    SubscriptionService,
    SubscriptionRepository,
    FeatureAccessRepository,
    SubscriptionProviderForInternal,
    SubscriptionAccessGuard,
  ],
  exports: [
    SubscriptionService,
    SubscriptionProviderForInternal,
    SubscriptionAccessGuard,
  ],
})
export class SubscriptionModule {}
