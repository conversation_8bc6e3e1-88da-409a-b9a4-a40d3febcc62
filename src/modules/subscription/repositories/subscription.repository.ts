import { Injectable } from '@nestjs/common';
import { SubscriptionModel } from 'src/database/subscription/subscription.model';
import { SubscriptionEntity, SubscriptionStatusEnum } from '../entities/subscription.entity';

@Injectable()
export class SubscriptionRepository {
  
  async create(subscription: SubscriptionEntity): Promise<SubscriptionEntity> {
    const createdSubscription = new SubscriptionModel(subscription);
    await createdSubscription.save();
    return createdSubscription.toObject();
  }

  async findById(id: string): Promise<SubscriptionEntity | null> {
    const subscription = await SubscriptionModel.findOne({ id }).exec();
    return subscription !== null ? subscription.toObject() : null;
  }

  async findActiveSubscription(userId: string, planType?: string): Promise<SubscriptionEntity | null> {
    const now = new Date();
    const subscriptionDoc = await SubscriptionModel.findOne({
      userId,
      planType,
      status: SubscriptionStatusEnum.Active,
      endDate: { $gt: now },
    }).exec();
    return subscriptionDoc !== null ? subscriptionDoc.toObject() : null;
  }

  async findActiveSubscriptionByPlan(userId: string, planId: string): Promise<SubscriptionEntity | null> {
    const now = new Date();
    const subscriptionDoc = await SubscriptionModel.findOne({ 
      userId, 
      planId, 
      status: SubscriptionStatusEnum.Active, 
      endDate: { $gt: now } 
    }).exec();
    console.log(subscriptionDoc);
    return subscriptionDoc !== null ? subscriptionDoc.toObject() : null;
  }

  async findUserSubscriptions(userId: string): Promise<SubscriptionEntity[]> {
    const subscriptions = await SubscriptionModel.find({ userId }).exec();
    return subscriptions.map((item) => item.toObject());
  }

  async updateStatus(subscriptionId: string, status: SubscriptionStatusEnum): Promise<SubscriptionEntity> {
    const updatedDoc = await SubscriptionModel.findOneAndUpdate(
      { id: subscriptionId },
      { $set: { status } },
      { new: true, runValidators: true },
    ).exec();
    return updatedDoc !== null ? updatedDoc.toObject() : null;
  }

  async updateSubscriptionPlan(subscriptionId: string, planId: string, planType: string, endDate: Date): Promise<SubscriptionEntity> {
    const updatedDoc = await SubscriptionModel.findOneAndUpdate(
      { id: subscriptionId },
      { 
        $set: { 
          planId,
          planType,
          endDate,
          updatedAt: new Date()
        } 
      },
      { new: true, runValidators: true },
    ).exec();
    return updatedDoc !== null ? updatedDoc.toObject() : null;
  }
}
