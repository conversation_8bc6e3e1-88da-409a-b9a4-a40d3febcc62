import { Injectable } from '@nestjs/common';
import { FeatureAccessModel } from 'src/database/subscription/subscription.model';
import { AccessTypeEnum, FeatureAccessEntity } from '../entities/subscription.entity';
import { FeatureTypeEnum } from '../plans/common/entities/plans.entity';

@Injectable()
export class FeatureAccessRepository {

  async create(featureAccess: FeatureAccessEntity): Promise<FeatureAccessEntity> {
    const created = new FeatureAccessModel(featureAccess);
    await created.save();
    return created.toObject();
  }

  async findByUserAndFeature(userId: string, featureType: FeatureTypeEnum): Promise<FeatureAccessEntity | null> {
    const featureAccess = await FeatureAccessModel.findOne({
      userId,
      featureType,
      isActive: true,
    }).lean();
    return featureAccess;
  }

  async findActiveTrialAccess(userId: string, featureType: FeatureTypeEnum): Promise<FeatureAccessEntity | null> {
    const now = new Date();
    const trialAccess = await FeatureAccessModel.findOne({
      userId,
      featureType,
      accessType: AccessTypeEnum.Trial,
      isActive: true,
      endDate: { $gt: now },
    }).lean();
    return trialAccess;
  }

  async findTrialAccess(userId: string, featureType: FeatureTypeEnum): Promise<FeatureAccessEntity | null> {
    const trialAccess = await FeatureAccessModel.findOne({
      userId,
      featureType,
      accessType: AccessTypeEnum.Trial,
    }).lean();
    return trialAccess;
  }

  async incrementUsage(userId: string, featureType: FeatureTypeEnum): Promise<void> {
    await FeatureAccessModel.updateOne(
      { userId, featureType, isActive: true },
      { $inc: { usageCount: 1 } }
    );
  }

  async deactivateTrialAccess(userId: string, featureType: FeatureTypeEnum): Promise<void> {
    await FeatureAccessModel.updateMany(
      { userId, featureType, accessType: AccessTypeEnum.Trial },
      { isActive: false }
    );
  }

  async deactivateBySubscription(subscriptionId: string): Promise<void> {
    await FeatureAccessModel.updateMany(
      { subscriptionId },
      { isActive: false }
    );
  }

  async createOrUpdate(featureAccess: FeatureAccessEntity): Promise<FeatureAccessEntity> {
    const existing = await FeatureAccessModel.findOne({
      userId: featureAccess.userId,
      featureType: featureAccess.featureType,
      accessType: featureAccess.accessType,
    });

    if (existing) {
      await existing.updateOne(featureAccess);
      return existing.toObject();
    }
    
    return await this.create(featureAccess);
  }

  async findActivePaidAccess(userId: string, featureType: FeatureTypeEnum): Promise<FeatureAccessEntity | null> {
    const now = new Date();
    const paidAccess = await FeatureAccessModel.findOne({
      userId,
      featureType,
      accessType: AccessTypeEnum.Paid,
      isActive: true,
      endDate: { $gt: now },
    }).lean();
    return paidAccess;
  }

  async findLatestPaidAccess(userId: string, featureType: FeatureTypeEnum): Promise<FeatureAccessEntity | null> {
    const doc = await FeatureAccessModel.findOne({
      userId,
      featureType,
      accessType: AccessTypeEnum.Paid,
    })
      .sort({ endDate: -1 })
      .lean();
    return doc;
  }

  async expireAccess(userId: string, featureType: FeatureTypeEnum): Promise<void> {
    const now = new Date();
    await FeatureAccessModel.updateMany(
      { 
        userId, 
        featureType, 
        endDate: { $lt: now },
        isActive: true 
      },
      { isActive: false }
    );
  }
}
