import { Controller, Get, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PLANS_API_FOR_PUBLIC } from '../common/const/swagger.const';
import { GetPlansSuccessResponseDtoForUser } from '../dtos/user-get-plan.dto';
import { PublicUserPlanService } from '../services/user-public.service';

@ApiTags(PLANS_API_FOR_PUBLIC)
@Controller('public/plans')
export class PublicUserPlanController {
  constructor(private readonly userPublicPlanService: PublicUserPlanService) {}

  @Get()
  @ApiOperation({ summary: 'Get multiple plans' })
  @ApiResponse({
    description: 'Get multiple plans available',
    type: GetPlansSuccessResponseDtoForUser,
    status: HttpStatus.OK,
  })
  async getUserPlans() {
    return this.userPublicPlanService.getMultiplePlans();
  }
}
