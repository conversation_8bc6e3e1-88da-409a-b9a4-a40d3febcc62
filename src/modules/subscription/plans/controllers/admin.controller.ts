import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Parse<PERSON><PERSON><PERSON>ip<PERSON>,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { PLANS_API_FOR_ADMIN } from '../common/const/swagger.const';
import {
  CreatePlanRequestDto,
  CreatePlanSuccessResponseDto,
} from '../dtos/admin-create-plan.dto';
import {
  GetPlansSuccessResponseDtoForAdmin,
  GetPlanSuccessResponseDtoForAdmin,
} from '../dtos/admin-get-plan.dto';
import {
  UpdatePlanRequestDto,
  UpdatePlanSuccessResponseDto,
} from '../dtos/admin-update-plan.dto';
import { AdminPlanService } from '../services/admin.service';

@ApiTags(PLANS_API_FOR_ADMIN)
@UseGuards(new RolesGuard(['admin']))
@ApiBearerAuth()
@Controller('admin/plans')
export class AdminPlanController {
  constructor(private readonly adminPlanService: AdminPlanService) {}

  @Post()
  @ApiBody({
    description: 'New plan information',
    type: CreatePlanRequestDto,
  })
  @ApiResponse({
    description: 'Returns the newly created plan',
    type: CreatePlanSuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  async createPlan(
    @Body() createPlanDto: CreatePlanRequestDto,
  ): Promise<CreatePlanSuccessResponseDto> {
    return this.adminPlanService.createPlan(createPlanDto);
  }
  @Get()
  @ApiOperation({ summary: 'Get multiple plans' })
  @ApiResponse({
    description: 'Get multiple plans available',
    type: GetPlansSuccessResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  async getMultiplePlans() {
    return await this.adminPlanService.getMultiplePlans();
  }

  @Get(':planId')
  @ApiOperation({ summary: 'Get single plan' })
  @ApiParam({
    name: 'planId',
    description: 'The planId whose data you want',
    type: String,
  })
  @ApiResponse({
    description: 'Get single plan',
    type: GetPlanSuccessResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  async getSinglePlan(@Param('planId', ParseUUIDPipe) planId: string) {
    return await this.adminPlanService.getSinglePlan(planId);
  }

  @Patch(':planId')
  @ApiOperation({ summary: 'Update single plan' })
  @ApiParam({
    name: 'planId',
    description: 'The planId whose data you want to update',
    type: String,
  })
  @ApiBody({
    description: 'Update a plan',
    type: UpdatePlanRequestDto,
  })
  @ApiResponse({
    description: 'Returns the updated plan data',
    type: UpdatePlanSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async updateSinglePlan(
    @Param('planId', ParseUUIDPipe) planId: string,
    @Body() updateDto: UpdatePlanRequestDto,
  ) {
    return await this.adminPlanService.updateSinglePlan(planId, updateDto);
  }

  @Delete(':planId')
  @ApiOperation({ summary: 'Delete single plan' })
  @ApiParam({
    name: 'planId',
    description: 'The planId whose data you want to delete',
    type: String,
  })
  @ApiResponse({
    description: 'Returns the deleted plan data',
    type: GetPlanSuccessResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  async deletePlan(@Param('planId', ParseUUIDPipe) planId: string) {
    return await this.adminPlanService.deleteSinglePlan(planId);
  }
}
