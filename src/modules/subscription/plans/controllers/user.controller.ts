import { Controller, Get, HttpStatus, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { PLANS_API_FOR_USER } from '../common/const/swagger.const';
import { GetPlansSuccessResponseDtoForUser } from '../dtos/user-get-plan.dto';
import { UserPlanService } from '../services/user.service';

@ApiTags(PLANS_API_FOR_USER)
@UseGuards(new RolesGuard(['user']))
@ApiBearerAuth()
@Controller('user/plans')
export class UserPlanController {
  constructor(private readonly userPlanService: UserPlanService) {}

  @Get()
  @ApiOperation({ summary: 'Get multiple plans' })
  @ApiResponse({
    description: 'Get multiple plans available',
    type: GetPlansSuccessResponseDtoForUser,
    status: HttpStatus.OK,
  })
  async getUserPlans() {
    return this.userPlanService.getMultiplePlans();
  }
}
