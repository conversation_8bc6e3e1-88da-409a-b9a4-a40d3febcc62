import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ServiceSuccessResponse } from 'src/helper/serviceResponse/service.response.interface';
import {
  DurationType,
  FeatureTypeEnum,
  PlanTypeEnum,
} from '../common/entities/plans.entity';

class DescriptionDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  details: string;
}

class FeatureEntityRequestDto {
  @ApiProperty({ required: true, enum: FeatureTypeEnum })
  @IsEnum(FeatureTypeEnum)
  @IsNotEmpty()
  featureType: FeatureTypeEnum;

  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  trialDurationDays: number;

  @ApiProperty({ required: false, type: Number })
  @IsNumber()
  @IsOptional()
  trialUsageLimit?: number;

  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;
}

class FeatureEntityResponseDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, enum: FeatureTypeEnum })
  @IsEnum(FeatureTypeEnum)
  @IsNotEmpty()
  featureType: FeatureTypeEnum;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  description: string;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  trialDurationDays: number;

  @Expose()
  @ApiProperty({ required: false, type: Number })
  @IsNumber()
  trialUsageLimit?: number;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;
}

export class UpdatePlanRequestDto {
  @ApiProperty({ required: false, type: String })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ required: false, type: [DescriptionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DescriptionDto)
  @IsOptional()
  description?: DescriptionDto[];

  @ApiProperty({ required: false, type: Number })
  @IsNumber()
  @IsOptional()
  price?: number;

  @ApiProperty({ required: false, type: Number })
  @IsNumber()
  @IsOptional()
  originalPrice?: number;

  @ApiProperty({ required: false, enum: PlanTypeEnum })
  @IsEnum(PlanTypeEnum)
  @IsOptional()
  planType?: PlanTypeEnum;

  @ApiProperty({ required: false, type: Boolean })
  @IsBoolean()
  @IsOptional()
  isPopular?: boolean;

  @ApiProperty({ required: false, type: Number })
  @IsNumber()
  @IsOptional()
  durationDays?: number;

  @ApiProperty({ required: false, enum: DurationType })
  @IsEnum(DurationType)
  @IsOptional()
  durationType?: DurationType;

  @ApiProperty({ required: false, type: Boolean })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    required: false,
    type: [String],
    description: 'Array of feature IDs',
    example: ['feature-id-1', 'feature-id-2'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  @IsOptional()
  featureIds?: string[];
}

export class UpdatePlanResponseDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Expose()
  @ApiProperty({ required: false, type: [DescriptionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DescriptionDto)
  description?: DescriptionDto[];

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  price: number;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  originalPrice: number;

  @Expose()
  @ApiProperty({ required: true, enum: PlanTypeEnum })
  @IsEnum(PlanTypeEnum)
  @IsNotEmpty()
  planType: PlanTypeEnum;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isPopular: boolean;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  durationDays: number;

  @Expose()
  @ApiProperty({ required: true, enum: DurationType })
  @IsEnum(DurationType)
  @IsNotEmpty()
  durationType: DurationType;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;

  @Expose()
  @ApiProperty({ required: true, type: [FeatureEntityResponseDto] })
  @IsObject({ each: true })
  @ValidateNested({ each: true })
  @IsArray()
  features: FeatureEntityResponseDto[];
}

export class UpdatePlanSuccessResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({ required: true, type: UpdatePlanResponseDto })
  @IsObject()
  @IsNotEmpty()
  data: UpdatePlanResponseDto;
}
