import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ServiceSuccessResponse } from 'src/helper/serviceResponse/service.response.interface';
import {
  DurationType,
  FeatureTypeEnum,
  PlanTypeEnum,
} from '../common/entities/plans.entity';

class FeatureEntityRequestDto {
  @ApiProperty({ required: true, enum: FeatureTypeEnum })
  @IsEnum(FeatureTypeEnum)
  @IsNotEmpty()
  featureType: FeatureTypeEnum;

  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  trialDurationDays: number;

  @ApiProperty({ required: false, type: Number })
  @IsNumber()
  @IsOptional()
  trialUsageLimit?: number;

  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;
}

class FeatureEntityResponseDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, enum: FeatureTypeEnum })
  @IsEnum(FeatureTypeEnum)
  @IsNotEmpty()
  featureType: FeatureTypeEnum;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  description: string;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  trialDurationDays: number;

  @Expose()
  @ApiProperty({ required: false, type: Number })
  @IsNumber()
  trialUsageLimit?: number;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;
}

class DescriptionDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  details: string;
}

export class CreatePlanRequestDto {
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ required: false, type: [DescriptionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DescriptionDto)
  @IsOptional()
  description: DescriptionDto[];

  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  price: number;

  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  originalPrice: number;

  @ApiProperty({ required: true, enum: PlanTypeEnum })
  @IsEnum(PlanTypeEnum)
  @IsNotEmpty()
  planType: PlanTypeEnum;

  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isPopular: boolean;

  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  durationDays: number;

  @ApiProperty({ required: true, enum: DurationType })
  @IsEnum(DurationType)
  @IsNotEmpty()
  durationType: DurationType;

  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;

  @ApiProperty({
    required: true,
    type: [String],
    description: 'Array of feature IDs',
    example: ['feature-id-1', 'feature-id-2'],
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  featureIds: string[];

  // @ApiProperty({ required: true, type: [FeatureEntityRequestDto] })
  // @IsObject({ each: true })
  // @ValidateNested({ each: true })
  // @IsArray()
  // features: FeatureEntityRequestDto[];
}

export class CreatePlanResponseDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Expose()
  @ApiProperty({ required: false, type: [DescriptionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DescriptionDto)
  description: DescriptionDto[];

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  price: number;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  originalPrice: number;

  @Expose()
  @ApiProperty({ required: true, enum: PlanTypeEnum })
  @IsEnum(PlanTypeEnum)
  @IsNotEmpty()
  planType: PlanTypeEnum;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isPopular: boolean;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  durationDays: number;

  @Expose()
  @ApiProperty({ required: true, enum: DurationType })
  @IsEnum(DurationType)
  @IsNotEmpty()
  durationType: DurationType;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;

  @Expose()
  @ApiProperty({ required: true, type: [FeatureEntityResponseDto] })
  @IsObject({ each: true })
  @ValidateNested({ each: true })
  @IsArray()
  features: FeatureEntityResponseDto[];
}

export class CreatePlanSuccessResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({ required: true, type: CreatePlanResponseDto })
  @IsObject()
  @IsNotEmpty()
  data: CreatePlanResponseDto;
}
