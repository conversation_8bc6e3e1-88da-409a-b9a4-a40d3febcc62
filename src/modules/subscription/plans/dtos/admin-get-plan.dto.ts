import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ServiceSuccessResponse } from 'src/helper/serviceResponse/service.response.interface';
import {
  DurationType,
  FeatureTypeEnum,
  PlanTypeEnum,
} from '../common/entities/plans.entity';

class FeatureEntityResponseDtoForAdmin {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, enum: FeatureTypeEnum })
  @IsEnum(FeatureTypeEnum)
  @IsNotEmpty()
  featureType: FeatureTypeEnum;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  description: string;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  trialDurationDays: number;

  @Expose()
  @ApiProperty({ required: false, type: Number })
  @IsNumber()
  trialUsageLimit?: number;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;
}

class DescriptionDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  details: string;
}

export class GetPlanResponseDtoForAdmin {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Expose()
  @ApiProperty({ required: false, type: [DescriptionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DescriptionDto)
  description: DescriptionDto[];

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  price: number;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  originalPrice: number;

  @Expose()
  @ApiProperty({ required: true, enum: PlanTypeEnum })
  @IsEnum(PlanTypeEnum)
  @IsNotEmpty()
  planType: PlanTypeEnum;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isPopular: boolean;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  durationDays: number;

  @Expose()
  @ApiProperty({ required: true, enum: DurationType })
  @IsEnum(DurationType)
  @IsNotEmpty()
  durationType: DurationType;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;

  @Expose()
  @ApiProperty({ required: true, type: [FeatureEntityResponseDtoForAdmin] })
  @IsObject({ each: true })
  @ValidateNested({ each: true })
  @IsArray()
  features: FeatureEntityResponseDtoForAdmin[];

  @Expose()
  @ApiProperty({ required: true, type: Date })
  @IsNotEmpty()
  createdAt: Date;

  @Expose()
  @ApiProperty({ required: true, type: Date })
  @IsNotEmpty()
  updatedAt: Date;
}

export class GetPlanSuccessResponseDtoForAdmin
  implements ServiceSuccessResponse
{
  @Expose()
  @ApiProperty({ required: true, type: GetPlanResponseDtoForAdmin })
  @IsObject()
  @IsNotEmpty()
  data: GetPlanResponseDtoForAdmin;
}

export class GetPlansResponseDtoForAdmin {
  @Expose()
  @ApiProperty({ required: true, type: [GetPlanResponseDtoForAdmin] })
  @IsObject({ each: true })
  @ValidateNested({ each: true })
  @IsArray()
  plans: GetPlanResponseDtoForAdmin[];
}

export class GetPlansSuccessResponseDtoForAdmin
  implements ServiceSuccessResponse
{
  @Expose()
  @ApiProperty({ required: true, type: [GetPlanResponseDtoForAdmin] })
  @IsObject({ each: true })
  @ValidateNested({ each: true })
  @IsArray()
  data: GetPlanResponseDtoForAdmin[];
}
