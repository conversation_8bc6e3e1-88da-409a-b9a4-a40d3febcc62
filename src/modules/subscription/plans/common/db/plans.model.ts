import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import {
  DurationType,
  FeatureEntity,
  FeatureTypeEnum,
  PlansEntity,
  PlanTypeEnum,
} from '../entities/plans.entity';

const FeatureEntitySchema = new Schema<FeatureEntity>(
  {
    id: {
      type: String,
      required: true,
    },
    featureType: {
      type: String,
      enum: FeatureTypeEnum,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    trialDurationDays: {
      type: Number,
      required: true,
    },
    trialUsageLimit: {
      type: Number,
      required: false,
    },
    isActive: {
      type: Boolean,
      required: true,
    },
  },
  {
    _id: false,
    timestamps: true,
    versionKey: false,
  },
);

const PlansEntitySchema = new Schema<PlansEntity>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    name: {
      type: String,
      required: true,
    },
    description: {
      type: [
        {
          name: {
            type: String,
            required: true,
          },
          details: {
            type: String,
            required: true,
          },
        },
      ],
      required: true,
    },
    price: {
      type: Number,
      required: true,
    },
    originalPrice: {
      type: Number,
      required: true,
    },
    planType: {
      type: String,
      enum: PlanTypeEnum,
      // required: true,
    },
    isPopular: {
      type: Boolean,
      required: true,
    },
    durationDays: {
      type: Number,
      required: true,
    },
    durationType: {
      type: String,
      enum: DurationType,
      required: true,
    },
    isActive: {
      type: Boolean,
      required: true,
    },
    features: {
      type: [FeatureEntitySchema],
      required: true,
    },
    isDeleted: {
      type: Boolean,
      required: true,
      default: false,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const PlansEntityModel = model<PlansEntity>('plans', PlansEntitySchema);
export { PlansEntityModel };
