export class PlansEntity {
  id: string;
  name: string;
  description: {
    name: string;
    details: string;
  }[];
  price: number;
  originalPrice: number;
  planType: PlanTypeEnum;
  isPopular: boolean;
  durationDays: number;
  durationType: DurationType;
  isActive: boolean;
  features: FeatureEntity[];
  isDeleted: boolean;
}

export class FeatureEntity {
  id: string;
  featureType: FeatureTypeEnum;
  name: string;
  description: string;
  trialDurationDays: number;
  trialUsageLimit?: number;
  isActive: boolean;
}

export enum PlanTypeEnum {
  FoodScannerOnly = 'FoodScannerOnly',
  Basic = 'Basic',
  Premium = 'Premium',
  Pro = 'Pro',
}

export enum FeatureTypeEnum {
  FoodScanner = 'FoodScanner',
}

export enum DurationType {
  YEAR = 'year',
  MONTH = 'month',
  SIXMONTH = 'sixmonth',
  ONETIME = 'onetime',
}

export interface CreatePlanData {
  name: string;
  description: {
    name: string;
    details: string;
  }[];
  price: number;
  originalPrice: number;
  planType: PlanTypeEnum;
  isPopular: boolean;
  durationDays: number;
  durationType: DurationType;
  isActive: boolean;
  features: FeatureEntity[];
  isDeleted: boolean;
}
