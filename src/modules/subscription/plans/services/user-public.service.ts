import { Injectable } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import { deepCasting } from 'src/internal/casting/object.casting';
import {
  GetPlanResponseDtoForUser,
  GetPlansSuccessResponseDtoForUser,
} from '../dtos/user-get-plan.dto';
import { UserPlanRepository } from '../repositories/user.repository';

@Injectable()
export class PublicUserPlanService {
  constructor(
    private readonly userPlanRepository: UserPlanRepository,
    private readonly helper: Helper,
  ) {}

  async getMultiplePlans(): Promise<GetPlansSuccessResponseDtoForUser> {
    const planDocs = await this.userPlanRepository.getPlans();
    const responseDto = planDocs.map((item) =>
      deepCasting(GetPlanResponseDtoForUser, item),
    );
    return this.helper.serviceResponse.successResponse(responseDto);
  }
}
