import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import { deepCasting } from 'src/internal/casting/object.casting';
import { throwNotFoundErr } from 'src/internal/exception/api.exception.ext';
import { AdminFeatureProvider } from '../../features/providers/admin.provider';
import { PLAN_NOT_FOUND } from '../common/const/plan.const';
import {
  CreatePlanRequestDto,
  CreatePlanResponseDto,
  CreatePlanSuccessResponseDto,
} from '../dtos/admin-create-plan.dto';
import {
  GetPlanResponseDtoForAdmin,
  GetPlansSuccessResponseDtoForAdmin,
  GetPlanSuccessResponseDtoForAdmin,
} from '../dtos/admin-get-plan.dto';
import {
  UpdatePlanRequestDto,
  UpdatePlanResponseDto,
  UpdatePlanSuccessResponseDto,
} from '../dtos/admin-update-plan.dto';
import { AdminPlanRepository } from '../repositories/admin.repository';

@Injectable()
export class AdminPlanService {
  constructor(
    private readonly adminPlanRepository: AdminPlanRepository,
    private readonly helper: Helper,
    @Inject(forwardRef(() => AdminFeatureProvider))
    private readonly adminFeatureProvider: AdminFeatureProvider,
  ) {}

  async createPlan(
    data: CreatePlanRequestDto,
  ): Promise<CreatePlanSuccessResponseDto> {
    const features = await this.adminFeatureProvider.getFeaturesByIds(
      data.featureIds,
    );

    // console.log('features', features);

    // Validate that all requested features exist
    if (features.length !== data.featureIds.length) {
      //  throw new BadRequestException('One or more feature IDs are invalid');
    }

    // const createPlanData = shallowCasting(PlansEntity, data);
    const createPlanData = {
      name: data.name,
      description: data.description,
      price: data.price,
      originalPrice: data.originalPrice,
      planType: data.planType,
      isPopular: data.isPopular,
      durationDays: data.durationDays,
      durationType: data.durationType,
      isActive: data.isActive,
      features: features,
      isDeleted: false,
    };

    const createdPlan = await this.adminPlanRepository.createPlan(
      createPlanData,
    );
    // console.log('createdPlan', createdPlan);

    const responseDto = deepCasting(CreatePlanResponseDto, createdPlan);

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async updateSinglePlan(
    planId: string,
    updateDto: UpdatePlanRequestDto,
  ): Promise<UpdatePlanSuccessResponseDto> {
    let updatePlanData: any = {};

    // If featureIds are provided, fetch the actual features
    if (updateDto.featureIds && updateDto.featureIds.length > 0) {
      const features = await this.adminFeatureProvider.getFeaturesByIds(
        updateDto.featureIds,
      );

      // Validate that all requested features exist
      if (features.length !== updateDto.featureIds.length) {
        // throw new BadRequestException('One or more feature IDs are invalid');
      }

      // Create update data with features instead of featureIds
      const { featureIds, ...otherUpdateData } = updateDto;
      updatePlanData = {
        ...otherUpdateData,
        features: features,
      };
    } else {
      // If no featureIds provided, just use the other fields
      const { featureIds, ...otherUpdateData } = updateDto;
      updatePlanData = otherUpdateData;
    }

    const newObj = await this.adminPlanRepository.updatePlan(
      planId,
      updatePlanData,
    );
    throwNotFoundErr(!newObj, 'Plan not found!', PLAN_NOT_FOUND);

    const responseDto = deepCasting(UpdatePlanResponseDto, newObj);
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getMultiplePlans(): Promise<GetPlansSuccessResponseDtoForAdmin> {
    const planDocs = await this.adminPlanRepository.getPlans();
    const responseDto = planDocs.map((item) =>
      deepCasting(GetPlanResponseDtoForAdmin, item),
    );
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getSinglePlan(
    planId: string,
  ): Promise<GetPlanSuccessResponseDtoForAdmin> {
    const planDoc = await this.adminPlanRepository.getPlanById(planId);
    throwNotFoundErr(!planDoc, 'Plan not found!', PLAN_NOT_FOUND);

    const responseDto = deepCasting(GetPlanResponseDtoForAdmin, planDoc);
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async deleteSinglePlan(planId: string): Promise<any> {
    const planDoc = await this.adminPlanRepository.deletePlan(planId);
    throwNotFoundErr(!planDoc, 'Plan not found!', PLAN_NOT_FOUND);

    // const responseDto = deepCasting(GetPlanResponseDtoForAdmin, planDoc);
    return this.helper.serviceResponse.successResponse({
      message: 'Plan deleted successfully',
    });
  }
}
