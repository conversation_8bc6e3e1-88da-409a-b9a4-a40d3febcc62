import { Injectable } from '@nestjs/common';
import { PlansEntityModel } from '../common/db/plans.model';
import { PlansEntity } from '../common/entities/plans.entity';

@Injectable()
export class AdminPlanRepository {
  async createPlan(data: any): Promise<PlansEntity> {
    const newPlan = new PlansEntityModel(data);
    const savedPlan = await newPlan.save();
    return savedPlan.toObject();
  }

  async getPlanById(planId: string): Promise<PlansEntity | null> {
    const searchQuery = { id: planId, isDeleted: false };
    const planDoc = await PlansEntityModel.findOne(searchQuery).exec();
    return planDoc !== null ? planDoc.toObject() : null;
  }

  async getPlans(): Promise<PlansEntity[]> {
    const searchQuery = { isDeleted: false };
    const categoryDoc = await PlansEntityModel.find(searchQuery).exec();
    return categoryDoc.map((item) => item.toObject());
  }

  async updatePlan(
    planId: string,
    info: PlansEntity,
  ): Promise<PlansEntity | null> {
    const updatedDoc = await PlansEntityModel.findOneAndUpdate(
      { id: planId, isDeleted: false },
      { $set: info },
      { new: true, runValidators: true },
    ).exec();
    return updatedDoc !== null ? updatedDoc.toObject() : null;
  }

  async deletePlan(planId: string): Promise<PlansEntity | null> {
    const newInfo = { isDeleted: true };
    const updatedDoc = await PlansEntityModel.findOneAndUpdate(
      { id: planId, isDeleted: false },
      { $set: newInfo },
      { new: true, runValidators: true },
    ).exec();
    return updatedDoc !== null ? updatedDoc.toObject() : null;
  }
}
