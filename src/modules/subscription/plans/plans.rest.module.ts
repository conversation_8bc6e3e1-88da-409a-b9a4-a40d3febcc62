import { forwardRef, Module } from '@nestjs/common';
import { FeaturesModule } from '../features/features.rest.module';
import { AdminFeatureProvider } from '../features/providers/admin.provider';
import { AdminFeatureRepository } from '../features/repositories/admin.repository';
import { AdminPlanController } from './controllers/admin.controller';
import { PublicUserPlanController } from './controllers/user-public.controller';
import { UserPlanController } from './controllers/user.controller';
import { AdminPlanRepository } from './repositories/admin.repository';
import { UserPlanRepository } from './repositories/user.repository';
import { AdminPlanService } from './services/admin.service';
import { PublicUserPlanService } from './services/user-public.service';
import { UserPlanService } from './services/user.service';

@Module({
  imports: [forwardRef(() => FeaturesModule)],
  controllers: [
    AdminPlanController,
    UserPlanController,
    PublicUserPlanController,
  ],
  providers: [
    AdminPlanService,
    UserPlanService,
    PublicUserPlanService,
    AdminPlanRepository,
    UserPlanRepository,
    AdminFeatureProvider,
    AdminFeatureRepository,
  ],
  exports: [UserPlanRepository],
})
export class PlansModule {}
