import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { ServiceSuccessResponse } from 'src/helper/serviceResponse/service.response.interface';
import { FeatureTypeEnum } from '../common/entities/features.entity';

export class CreateFeatureRequestDto {
  @ApiProperty({ required: true, enum: FeatureTypeEnum })
  @IsEnum(FeatureTypeEnum)
  @IsNotEmpty()
  featureType: FeatureTypeEnum;

  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ required: false, type: String })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  trialDurationDays: number;

  @ApiProperty({ required: false, type: Number })
  @IsNumber()
  @IsOptional()
  trialUsageLimit?: number;

  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;
}

export class CreateFeatureResponseDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, enum: FeatureTypeEnum })
  @IsEnum(FeatureTypeEnum)
  @IsNotEmpty()
  featureType: FeatureTypeEnum;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  description: string;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  trialDurationDays: number;

  @Expose()
  @ApiProperty({ required: false, type: Number })
  @IsNumber()
  trialUsageLimit?: number;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;
}

export class CreateFeatureSuccessResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({ required: true, type: CreateFeatureResponseDto })
  @IsObject()
  @IsNotEmpty()
  data: CreateFeatureResponseDto;
}
