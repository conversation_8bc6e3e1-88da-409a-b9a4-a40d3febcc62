import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ServiceSuccessResponse } from 'src/helper/serviceResponse/service.response.interface';
import { FeatureTypeEnum } from '../common/entities/features.entity';

export class GetFeatureResponseDtoForAdmin {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, enum: FeatureTypeEnum })
  @IsEnum(FeatureTypeEnum)
  @IsNotEmpty()
  featureType: FeatureTypeEnum;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  description: string;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNotEmpty()
  @IsNumber()
  trialDurationDays: number;

  @Expose()
  @ApiProperty({ required: false, type: Number })
  @IsNumber()
  trialUsageLimit?: number;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;
}

export class GetFeatureSuccessResponseDtoForAdmin
  implements ServiceSuccessResponse
{
  @Expose()
  @ApiProperty({ required: true, type: GetFeatureResponseDtoForAdmin })
  @IsObject()
  @IsNotEmpty()
  data: GetFeatureResponseDtoForAdmin;
}

export class GetFeaturesSuccessResponseDtoForAdmin
  implements ServiceSuccessResponse
{
  @Expose()
  @ApiProperty({ required: true, type: [GetFeatureResponseDtoForAdmin] })
  @IsObject({ each: true })
  @ValidateNested({ each: true })
  @IsArray()
  data: GetFeatureResponseDtoForAdmin[];
}
