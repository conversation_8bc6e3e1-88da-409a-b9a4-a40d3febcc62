import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { ServiceSuccessResponse } from 'src/helper/serviceResponse/service.response.interface';
import { FeatureTypeEnum } from '../common/entities/features.entity';

export class UpdateFeatureRequestDto {
  @ApiProperty({ required: true, enum: FeatureTypeEnum })
  @IsEnum(FeatureTypeEnum)
  @IsOptional()
  featureType: FeatureTypeEnum;

  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty({ required: true, type: Number })
  @IsOptional()
  @IsNumber()
  trialDurationDays: number;

  @ApiProperty({ required: false, type: Number })
  @IsNumber()
  @IsOptional()
  trialUsageLimit?: number;

  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsOptional()
  isActive: boolean;
}

class UpdateFeatureResponseDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsOptional()
  id: string;

  @Expose()
  @ApiProperty({ required: true, enum: FeatureTypeEnum })
  @IsEnum(FeatureTypeEnum)
  @IsOptional()
  featureType: FeatureTypeEnum;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsOptional()
  name: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsOptional()
  description: string;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsOptional()
  @IsNumber()
  trialDurationDays: number;

  @Expose()
  @ApiProperty({ required: false, type: Number })
  @IsNumber()
  trialUsageLimit?: number;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsOptional()
  isActive: boolean;
}

export class UpdateFeatureSuccessResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({ required: true, type: UpdateFeatureResponseDto })
  @IsObject()
  @IsNotEmpty()
  data: UpdateFeatureResponseDto;
}
