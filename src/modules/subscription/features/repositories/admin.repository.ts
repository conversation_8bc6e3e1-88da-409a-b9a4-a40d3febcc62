import { Injectable } from '@nestjs/common';
import { FeatureEntityModel } from '../common/db/features.model';
import { FeatureEntity } from '../common/entities/features.entity';
import { CreateFeatureRequestDto } from '../dtos/admin-create-feature.dto';

@Injectable()
export class AdminFeatureRepository {
  async createFeature(data: CreateFeatureRequestDto): Promise<FeatureEntity> {
    const newFeature = new FeatureEntityModel(data);
    const savedFeature = await newFeature.save();
    return savedFeature.toObject();
  }

  async getFeatureById(featureId: string): Promise<FeatureEntity | null> {
    const searchQuery = {
      id: featureId,
      isActive: true,
      isDeleted: false,
    };
    const featureDoc = await FeatureEntityModel.findOne(searchQuery).exec();
    return featureDoc !== null ? featureDoc.toObject() : null;
  }

  async getFeatures(): Promise<FeatureEntity[]> {
    const searchQuery = { isDeleted: false, isActive: true };
    const featureDoc = await FeatureEntityModel.find(searchQuery).exec();
    return featureDoc.map((item) => item.toObject());
  }

  async updateFeature(
    featureId: string,
    info: FeatureEntity,
  ): Promise<FeatureEntity | null> {
    const updatedDoc = await FeatureEntityModel.findOneAndUpdate(
      { id: featureId, isDeleted: false },
      { $set: info },
      { new: true, runValidators: true },
    ).exec();
    return updatedDoc !== null ? updatedDoc.toObject() : null;
  }

  async deleteFeature(featureId: string): Promise<FeatureEntity | null> {
    const newInfo = { isDeleted: true };
    const updatedDoc = await FeatureEntityModel.findOneAndUpdate(
      { id: featureId, isDeleted: false },
      { $set: newInfo },
      { new: true, runValidators: true },
    ).exec();
    return updatedDoc !== null ? updatedDoc.toObject() : null;
  }

  async getFeaturesByIds(featureIds: string[]): Promise<FeatureEntity[]> {
    const searchQuery = {
      id: { $in: featureIds },
      isActive: true,
      isDeleted: false,
    };
    const featureDocs = await FeatureEntityModel.find(searchQuery)
      .select('-_id')
      .exec();
    return featureDocs.map((item) => item.toObject());
  }
}
