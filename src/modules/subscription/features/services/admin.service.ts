import { Injectable } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import {
  deepCasting,
  shallowCasting,
} from 'src/internal/casting/object.casting';
import { throwNotFoundErr } from 'src/internal/exception/api.exception.ext';
import { FEATURE_NOT_FOUND } from '../common/const/features.const';
import { FeatureEntity } from '../common/entities/features.entity';
import {
  CreateFeatureRequestDto,
  CreateFeatureResponseDto,
  CreateFeatureSuccessResponseDto,
} from '../dtos/admin-create-feature.dto';
import {
  GetFeatureResponseDtoForAdmin,
  GetFeaturesSuccessResponseDtoForAdmin,
  GetFeatureSuccessResponseDtoForAdmin,
} from '../dtos/admin-get-feature.dto';
import {
  UpdateFeatureRequestDto,
  UpdateFeatureSuccessResponseDto,
} from '../dtos/admin-update-feature.dto';
import { AdminFeatureRepository } from '../repositories/admin.repository';

@Injectable()
export class AdminFeatureService {
  constructor(
    private readonly adminFeatureRepository: AdminFeatureRepository,
    private readonly helper: Helper,
  ) {}

  async createFeature(
    data: CreateFeatureRequestDto,
  ): Promise<CreateFeatureSuccessResponseDto> {
    const createFeatureData = shallowCasting(FeatureEntity, data);
    const feature = await this.adminFeatureRepository.createFeature(
      createFeatureData,
    );
    const responseDto = deepCasting(CreateFeatureResponseDto, feature);

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getMultipleFeatures(): Promise<GetFeaturesSuccessResponseDtoForAdmin> {
    const featureDocs = await this.adminFeatureRepository.getFeatures();
    const responseDto = featureDocs.map((item) =>
      deepCasting(GetFeatureResponseDtoForAdmin, item),
    );
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getSingleFeature(
    featureId: string,
  ): Promise<GetFeatureSuccessResponseDtoForAdmin> {
    const featureDoc = await this.adminFeatureRepository.getFeatureById(
      featureId,
    );
    throwNotFoundErr(!featureDoc, 'Feature not found!', FEATURE_NOT_FOUND);

    const responseDto = deepCasting(GetFeatureResponseDtoForAdmin, featureDoc);
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async updateSingleFeature(
    featureId: string,
    updateDto: UpdateFeatureRequestDto,
  ): Promise<UpdateFeatureSuccessResponseDto> {
    const featureInfo = shallowCasting(FeatureEntity, updateDto);
    const newObj = await this.adminFeatureRepository.updateFeature(
      featureId,
      featureInfo,
    );
    throwNotFoundErr(!newObj, 'Feature not found!', FEATURE_NOT_FOUND);

    const responseDto = deepCasting(UpdateFeatureSuccessResponseDto, newObj);
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async deleteSingleFeature(featureId: string): Promise<any> {
    const featureDoc = await this.adminFeatureRepository.deleteFeature(
      featureId,
    );
    throwNotFoundErr(!featureDoc, 'Feature not found!', FEATURE_NOT_FOUND);

    // const responseDto = deepCasting(GetFeatureResponseDtoForAdmin, featureDoc);
    return this.helper.serviceResponse.successResponse({
      message: 'Feature deleted successfully',
    });
  }
}
