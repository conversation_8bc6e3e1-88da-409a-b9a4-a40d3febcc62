import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseU<PERSON><PERSON>ip<PERSON>,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { FEATURES_API_FOR_ADMIN } from '../common/const/swagger.const';
import {
  CreateFeatureRequestDto,
  CreateFeatureSuccessResponseDto,
} from '../dtos/admin-create-feature.dto';
import {
  GetFeaturesSuccessResponseDtoForAdmin,
  GetFeatureSuccessResponseDtoForAdmin,
} from '../dtos/admin-get-feature.dto';
import {
  UpdateFeatureRequestDto,
  UpdateFeatureSuccessResponseDto,
} from '../dtos/admin-update-feature.dto';
import { AdminFeatureService } from '../services/admin.service';

@ApiTags(FEATURES_API_FOR_ADMIN)
@UseGuards(new RolesGuard(['admin']))
@ApiBearerAuth()
@Controller('admin/features')
export class AdminFeatureController {
  constructor(private readonly adminFeatureService: AdminFeatureService) {}

  @Post()
  @ApiBody({
    description: 'New feature information',
    type: CreateFeatureRequestDto,
  })
  @ApiResponse({
    description: 'Returns the newly created feature',
    type: CreateFeatureSuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  async createFeature(
    @Body() createFeatureDto: CreateFeatureRequestDto,
  ): Promise<CreateFeatureSuccessResponseDto> {
    return this.adminFeatureService.createFeature(createFeatureDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get multiple features' })
  @ApiResponse({
    description: 'Get multiple features available',
    type: GetFeaturesSuccessResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  async getMultipleFeatures() {
    return await this.adminFeatureService.getMultipleFeatures();
  }

  @Get(':featureId')
  @ApiOperation({ summary: 'Get single feature' })
  @ApiParam({
    name: 'featureId',
    description: 'The featureId whose data you want',
    type: String,
  })
  @ApiResponse({
    description: 'Get single feature',
    type: GetFeatureSuccessResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  async getSingleFeature(@Param('featureId', ParseUUIDPipe) featureId: string) {
    return await this.adminFeatureService.getSingleFeature(featureId);
  }

  @Patch(':featureId')
  @ApiOperation({ summary: 'Update single feature' })
  @ApiParam({
    name: 'featureId',
    description: 'The featureId whose data you want to update',
    type: String,
  })
  @ApiBody({
    description: 'Update a feature',
    type: UpdateFeatureRequestDto,
  })
  @ApiResponse({
    description: 'Returns the updated feature data',
    type: UpdateFeatureSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async updateSingleFeature(
    @Param('featureId', ParseUUIDPipe) featureId: string,
    @Body() updateDto: UpdateFeatureRequestDto,
  ) {
    return await this.adminFeatureService.updateSingleFeature(
      featureId,
      updateDto,
    );
  }

  @Delete(':featureId')
  @ApiOperation({ summary: 'Delete single feature' })
  @ApiParam({
    name: 'featureId',
    description: 'The featureId whose data you want to delete',
    type: String,
  })
  @ApiResponse({
    description: 'Returns the deleted feature data',
    type: GetFeatureSuccessResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  async deleteFeature(@Param('featureId', ParseUUIDPipe) featureId: string) {
    return await this.adminFeatureService.deleteSingleFeature(featureId);
  }
}
