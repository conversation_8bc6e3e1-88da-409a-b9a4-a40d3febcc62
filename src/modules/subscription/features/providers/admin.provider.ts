import { Injectable } from '@nestjs/common';
import { FeatureEntity } from '../common/entities/features.entity';
import { AdminFeatureRepository } from '../repositories/admin.repository';

@Injectable()
export class AdminFeatureProvider {
  constructor(
    private readonly adminFeatureRepository: AdminFeatureRepository,
  ) {}

  async getFeaturesByIds(featureIds: string[]): Promise<FeatureEntity[]> {
    return await this.adminFeatureRepository.getFeaturesByIds(featureIds);
  }

  async getFeatureById(featureId: string): Promise<FeatureEntity | null> {
    return await this.adminFeatureRepository.getFeatureById(featureId);
  }
}
