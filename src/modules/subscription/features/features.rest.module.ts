import { Module } from '@nestjs/common';
import { AdminFeatureController } from './controllers/admin.controller';

import { AdminFeatureProvider } from './providers/admin.provider';
import { AdminFeatureRepository } from './repositories/admin.repository';
import { AdminFeatureService } from './services/admin.service';

@Module({
  controllers: [AdminFeatureController],

  providers: [
    AdminFeatureService,
    AdminFeatureRepository,
    AdminFeatureProvider,
  ],

  exports: [AdminFeatureProvider],
})
export class FeaturesModule {}
