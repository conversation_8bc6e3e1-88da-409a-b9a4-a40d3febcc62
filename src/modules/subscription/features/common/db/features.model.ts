import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { FeatureEntity, FeatureTypeEnum } from '../entities/features.entity';

const FeatureEntitySchema = new Schema<FeatureEntity>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    featureType: {
      type: String,
      enum: FeatureTypeEnum,
      required: true,
    },
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: false,
    },
    trialDurationDays: {
      type: Number,
      required: true,
    },
    trialUsageLimit: {
      type: Number,
      required: false,
    },
    isActive: {
      type: Boolean,
      required: true,
    },
    isDeleted: {
      type: Boolean,
      required: true,
      default: false,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

const FeatureEntityModel = model<FeatureEntity>(
  'features',
  FeatureEntitySchema,
);
export { FeatureEntityModel };
