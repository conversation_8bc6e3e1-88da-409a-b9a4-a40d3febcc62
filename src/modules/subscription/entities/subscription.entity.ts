import { FeatureTypeEnum, PlanTypeEnum } from "../plans/common/entities/plans.entity";

export enum SubscriptionStatusEnum {
  Active = 'Active',
  Expired = 'Expired',
  Cancelled = 'Cancelled',
  PendingPayment = 'PendingPayment',
}

export enum AccessTypeEnum {
  Trial = 'Trial',
  Paid = 'Paid',
}

// User subscription entity
export class SubscriptionEntity {
  id?: string;
  userId: string;
  planId: string;
  planType: PlanTypeEnum;
  status: SubscriptionStatusEnum;
  startDate: Date;
  endDate: Date;
  autoRenew: boolean;
  paymentId?: string; // Reference to bKash payment
  createdAt?: Date;
  updatedAt?: Date;
}

// Feature access tracking entity
export class FeatureAccessEntity {
  id?: string;
  userId: string;
  featureType: FeatureTypeEnum;
  accessType: AccessTypeEnum;
  startDate: Date;
  endDate?: Date;
  usageCount: number;
  usageLimit?: number;
  subscriptionId?: string; // Reference if access is through subscription
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

// Access status response DTO
export class FeatureAccessStatusDto {
  featureType: FeatureTypeEnum;
  hasAccess: boolean;
  accessType: AccessTypeEnum;
  isTrialActive: boolean;
  trialEndsAt?: Date;
  subscriptionEndsAt?: Date;
  usageCount: number;
  usageLimit?: number;
  remainingUsage?: number;
}

// Subscription checkout request DTO
export class SubscriptionCheckoutDto {
  planId: string;
  autoRenew?: boolean;
}
