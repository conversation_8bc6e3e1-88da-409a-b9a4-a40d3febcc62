import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsArray, IsBoolean, IsDate, IsEnum, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString } from 'class-validator';
import { ServiceSuccessResponse } from 'src/helper/serviceResponse/service.response.interface';
import { SubscriptionStatusEnum } from '../entities/subscription.entity';
import { FeatureTypeEnum, PlanTypeEnum } from '../plans/common/entities/plans.entity';

export class SubscriptionCheckoutRequestDto {
  @ApiProperty({description: 'Subscription plan type'})
  @IsString()
  planId: string;

  @ApiPropertyOptional({
    description: 'Enable auto-renewal',
  })
  @IsOptional()
  @IsBoolean()
  autoRenew?: boolean;
}

export class FeatureAccessDto {
  @Expose()
  @ApiProperty({
    description: 'Feature type',
    enum: FeatureTypeEnum,
  })
  featureType: FeatureTypeEnum;

  @Expose()
  @ApiProperty({
    description: 'Whether user has access to this feature',
    example: true,
  })
  hasAccess: boolean;

  @Expose()
  @ApiProperty({
    description: 'Type of access (Trial or Paid)',
    example: 'Trial',
  })
  accessType: string;

  @Expose()
  @ApiProperty({
    description: 'Whether trial is currently active',
    example: true,
  })
  isTrialActive: boolean;

  @Expose()
  @ApiPropertyOptional({
    description: 'When trial ends',
    example: '2025-02-01T00:00:00.000Z',
  })
  trialEndsAt?: Date;

  @Expose()
  @ApiPropertyOptional({
    description: 'When subscription ends',
    example: '2025-03-01T00:00:00.000Z',
  })
  subscriptionEndsAt?: Date;

  @Expose()
  @ApiProperty({
    description: 'Current usage count',
    example: 7,
  })
  usageCount: number;

  @Expose()
  @ApiPropertyOptional({
    description: 'Usage limit (if any)',
    example: 10,
  })
  usageLimit?: number;

  @Expose()
  @ApiPropertyOptional({
    description: 'Remaining usage count',
    example: 3,
  })
  remainingUsage?: number;
}

export class FeatureAccessResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({description: 'Feature access details',})
  @IsObject()
  @IsNotEmpty()
  data: FeatureAccessDto;
}

export class CheckoutDto {
  @Expose()
  @ApiProperty({
    description: 'bKash payment URL for completing payment',
  })
  paymentURL: string;
}

export class CheckoutResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({description: 'Checkout details',})
  @IsObject()
  @IsNotEmpty()
  data: CheckoutDto;
}


export class PlanDto {
  @Expose()
  @IsString()
  @ApiProperty({
    description: 'Plan ID',
  })
  id?: string;

  @Expose()
  @IsEnum(PlanTypeEnum)
  @ApiProperty({
    description: 'Plan type',
    enum: PlanTypeEnum,
  })
  planType: PlanTypeEnum;

  @Expose()
  @IsString()
  @ApiProperty({
    description: 'Plan name',
    example: 'Pro Plan',
  })
  name: string;

  @Expose()
  @IsString()
  @ApiProperty({
    description: 'Plan description',
    example: 'Complete access with analytics',
  })
  description: string;

  @Expose()
  @IsNumber()
  @ApiProperty({
    description: 'Plan price (in BDT)',
    example: 399,
  })
  price: number;

  @Expose()
  @IsNumber()
  @ApiProperty({
    description: 'Plan duration (in days)',
    example: 30,
  })
  durationDays: number;

  @Expose()
  @IsArray()
  @ApiProperty({
    description: 'Features included in the plan',
    enum: FeatureTypeEnum,
    isArray: true,
  })
  features: FeatureTypeEnum[];

  @Expose()
  @IsBoolean()
  @ApiProperty({
    description: 'Whether the plan is active',
    example: true,
  })
  isActive: boolean;

  @Expose()
  @IsDate()
  @ApiPropertyOptional({
    description: 'Plan creation date',
  })
  createdAt?: Date;

  @Expose()
  @IsDate()
  @ApiPropertyOptional({
    description: 'Plan last update date',
  })
  updatedAt?: Date;
}

export class PlansResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({
    required: true,
    description: 'List of available plans',
    type: [PlanDto],
  })
  @IsArray()
  data: PlanDto[];
}

export class SubscriptionDto {
  @Expose()
  @IsString()
  @ApiProperty({
    description: 'Subscription ID',
  })
  id?: string;

  @Expose()
  @IsString()
  @ApiProperty({
    description: 'User ID',
  })
  userId: string;

  @Expose()
  @IsString()
  @ApiProperty({
    description: 'Plan ID',
  })
  planId: string; 

  @Expose()
  @IsEnum(SubscriptionStatusEnum)
  @ApiProperty({
    description: 'Subscription status',
    enum: SubscriptionStatusEnum,
  })
  status: SubscriptionStatusEnum;

  @Expose()
  @IsDate()
  @ApiProperty({
    description: 'Subscription start date',
  })
  startDate: Date;

  @Expose()
  @IsDate()
  @ApiProperty({
    description: 'Subscription end date',
  })
  endDate: Date;

  @Expose()
  @IsBoolean()
  @ApiProperty({
    description: 'Subscription auto-renewal status',
  })
  autoRenew: boolean;

  @Expose()
  @IsDate()
  @ApiProperty({
    description: 'Subscription creation date',
  })
  createdAt: Date;

  @Expose()
  @IsDate()
  @ApiProperty({
    description: 'Subscription last update date',
  })
  updatedAt: Date;
}

export class SubscriptionsResponseDto implements ServiceSuccessResponse {
  @ApiProperty({
    required: true,
    description: 'Subscription details',
    type: [SubscriptionDto]
  })
  @IsArray()
  @IsNotEmpty()
  data: SubscriptionDto[];
}