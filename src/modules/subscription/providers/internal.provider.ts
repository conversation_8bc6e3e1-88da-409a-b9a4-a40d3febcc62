import { Injectable } from '@nestjs/common';
import { SubscriptionService } from '../services/subscription.service';

@Injectable()
export class SubscriptionProviderForInternal {
  constructor(
    private readonly subscriptionService: SubscriptionService,
  ) {}

  /**
   * Handle successful bKash payment for subscription
   */
  async onBkashPaymentSuccessful(subscriptionId: string): Promise<void> {
    console.log(`Activating subscription after successful payment: ${subscriptionId}`);
    await this.subscriptionService.activateSubscription(subscriptionId);
  }

  /**
   * Handle failed bKash payment for subscription
   */
  async onBkashPaymentFailed(subscriptionId: string): Promise<void> {
    console.log(`Handling failed payment for subscription: ${subscriptionId}`);
    await this.subscriptionService.failedSubscription(subscriptionId);
    // Could implement logic to mark subscription as failed
  }

  /**
   * Handle cancelled bKash payment for subscription
   */
  async onBkashPaymentCancelled(subscriptionId: string): Promise<void> {
    console.log(`Handling cancelled payment for subscription: ${subscriptionId}`);
    await this.subscriptionService.cancelSubscription(subscriptionId);
    // Could implement logic to mark subscription as cancelled
  }
}
