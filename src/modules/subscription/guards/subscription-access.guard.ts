import { CanActivate, ExecutionContext, ForbiddenException, Injectable, SetMetadata, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { FeatureTypeEnum } from '../plans/common/entities/plans.entity';
import { SubscriptionService } from '../services/subscription.service';

export const REQUIRED_FEATURE = 'required_feature';
export const RequireFeature = (feature: FeatureTypeEnum) => SetMetadata(REQUIRED_FEATURE, feature);

@Injectable()
export class SubscriptionAccessGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private subscriptionService: SubscriptionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requiredFeature = this.reflector.getAllAndOverride<FeatureTypeEnum>(REQUIRED_FEATURE, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredFeature) {
      // No feature requirement set, allow access
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user.id) {
      throw new UnauthorizedException('Authentication required');
    }

    // Check if user has access to the required feature
    const hasAccess = await this.subscriptionService.recordUsageAndCheckAccess(user.id, requiredFeature);

    if (!hasAccess) {
      // Check access status for more detailed error message
      const accessStatus = await this.subscriptionService.checkFeatureAccess(user.id, requiredFeature);
      
      if (accessStatus.isTrialActive && accessStatus.remainingUsage === 0) {
        throw new ForbiddenException({
          message: `Trial usage limit reached for ${requiredFeature}. Please upgrade to continue.`,
          errorCode: 'TRIAL_LIMIT_REACHED',
          featureType: requiredFeature,
        });
      }
      
      if (!accessStatus.isTrialActive && !accessStatus.subscriptionEndsAt) {
        throw new ForbiddenException({
          message: `Trial period is over. Access to ${requiredFeature} requires a subscription.`,
          errorCode: 'SUBSCRIPTION_REQUIRED',
          featureType: requiredFeature,
        });
      }
      
      throw new ForbiddenException({
        message: `Subscription is expired. Access to ${requiredFeature} requires an active subscription.`,
        errorCode: 'SUBSCRIPTION_EXPIRED',
        featureType: requiredFeature,
      });
    }

    return true;
  }
}
