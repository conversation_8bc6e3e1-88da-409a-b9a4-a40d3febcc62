const {
  JWT_SECRET_KEY,
  JWT_EXPIRATION_TIME,
  SESSION_EXPIRATION_TIME,
  JWT_SALT,
  COOKIESMAXAGE,
  SIGNUP_OTP_EXPIRATION_TIME,
  SIGNUP_OTP_SUBJECT,
  SIGNUP_OTP_BODY_PREFIX,
  FORGOT_PASSWORD_OTP_EXPIRATION_TIME,
  FORGOT_PASSWORD_OTP_SUBJECT,
  FORGOT_PASSWORD_OTP_BODY_PREFIX,
  SET_NEW_PASSWORD_EXPIRATION_TIME,
  GOOGLE_USER_INFO_API_URL,
  FACEBOOK_USER_INFO_API_URL,
  APPLE_CLIENT_ID,
  APPLE_WEB_CLIENT_ID,
  APPLE_ANDROID_PACKAGE_IDENTIFIER,
  SOCIAL_DEFAULT_PASSWORD_POSTFIX,
  CHANGE_EMAIL_OTP_EXPIRATION_TIME,
  <PERSON>AN<PERSON>_EMAIL_OTP_SUBJECT,
  CHANGE_EMAIL_OTP_BODY_PREFIX,
} = process.env;

export const authConfig = {
  salt: parseInt(JWT_SALT) || 10,
  // expiration_time: JWT_EXPIRATION_TIME || '10d',
  expiration_time: JWT_EXPIRATION_TIME || '180d',
  session_expire_time:
    // parseInt(SESSION_EXPIRATION_TIME) || 10 * 24 * 60 * 60 * 1000,
    parseInt(SESSION_EXPIRATION_TIME) || 180 * 24 * 60 * 60 * 1000,
  jwt_key: JWT_SECRET_KEY || '@CTBS23@!46',
  cookiesMaxAge: parseInt(COOKIESMAXAGE) || 60 * 60 * 24 * 10,
  signup: {
    otp_expiration_time: parseInt(SIGNUP_OTP_EXPIRATION_TIME) || 3 * 60 * 1000, // 3 * 60 * 1000 means 3 minutes
    otp_subject: SIGNUP_OTP_SUBJECT || 'Fitsomnia One-Time Password (OTP)',
    otp_body_prefix: SIGNUP_OTP_BODY_PREFIX || 'Your FITSOMNIA OTP is',
  },
  forgot_password: {
    otp_expiration_time:
      parseInt(FORGOT_PASSWORD_OTP_EXPIRATION_TIME) || 3 * 60 * 1000,
    set_password_expiration_time:
      parseInt(SET_NEW_PASSWORD_EXPIRATION_TIME) || 3 * 60 * 1000,
    otp_subject:
      FORGOT_PASSWORD_OTP_SUBJECT || 'Fitsomnia One-Time Password (OTP)',
    otp_body_prefix: FORGOT_PASSWORD_OTP_BODY_PREFIX || 'Your FITSOMNIA OTP is',
  },
  change_email: {
    otp_expiration_time:
      parseInt(CHANGE_EMAIL_OTP_EXPIRATION_TIME) || 3 * 60 * 1000,
    otp_subject:
      CHANGE_EMAIL_OTP_SUBJECT || 'Fitsomnia One-Time Password (OTP)',
    otp_body_prefix: CHANGE_EMAIL_OTP_BODY_PREFIX || 'Your FITSOMNIA OTP is',
  },
  google: {
    base_url:
      GOOGLE_USER_INFO_API_URL ||
      'https://www.googleapis.com/oauth2/v3/userinfo',
  },
  facebook: {
    base_url: FACEBOOK_USER_INFO_API_URL || 'https://graph.facebook.com/me',
  },
  apple: {
    // Keep existing client ID for iOS (backward compatibility)
    iosClientId: APPLE_CLIENT_ID || 'com.fitsomnia.fitsomniaApp',
    // Add new web client ID for web support
    webClientId: APPLE_WEB_CLIENT_ID || 'com.fitsomnia.fitsomniaApp-service',
    packageIdentifier:
      APPLE_ANDROID_PACKAGE_IDENTIFIER || 'com.fitsomnia.fitsomnia_app',
    // Legacy field for backward compatibility
    clientId: APPLE_CLIENT_ID || 'com.fitsomnia.fitsomniaApp',
    supportedPlatforms: ['ios', 'web'],
  },
  social_default_password: SOCIAL_DEFAULT_PASSWORD_POSTFIX || 'password61',
};
