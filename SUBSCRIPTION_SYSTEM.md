# Fitsomnia Subscription System Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Core Components](#core-components)
4. [Features](#features)
5. [API Endpoints](#api-endpoints)
6. [Usage Examples](#usage-examples)
7. [Integration Patterns](#integration-patterns)
8. [Configuration](#configuration)
9. [Security](#security)
10. [Testing](#testing)

## Overview

The Fitsomnia subscription system is a comprehensive feature-based subscription management platform that supports:

- **Feature-based subscriptions** with plan bundling capability
- **Trial management** with configurable periods and usage limits
- **bKash payment integration** for subscription payments
- **Access control guards** for protecting premium features
- **Usage tracking** and analytics
- **Flexible pricing models** supporting both time-based and usage-based access

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Subscription Module                       │
├─────────────────────────────────────────────────────────────┤
│  Controllers  │  Services   │  Guards    │  Repositories    │
│  ─────────── │  ─────────  │  ────────  │  ──────────────  │
│  • REST API  │  • Business │  • Access  │  • Data Access   │
│  • Swagger   │    Logic    │    Control │  • MongoDB       │
│  • DTOs      │  • Trials   │  • Feature │  • Entities      │
│               │  • Payment  │    Gates   │                  │
├─────────────────────────────────────────────────────────────┤
│                  External Integrations                      │
│  ─────────────────────────────────────────────────────────  │
│  • bKash Payment Gateway                                    │
│  • User Authentication System                              │
│  • Notification Service                                     │
└─────────────────────────────────────────────────────────────┘
```

### Module Structure

```
src/modules/subscription/
├── controllers/
│   └── subscription.controller.ts      # REST API endpoints
├── services/
│   ├── subscription.service.ts         # Core business logic
│   └── feature.service.ts             # Feature management
├── repositories/
│   ├── subscription.repository.ts      # Subscription data access
│   ├── feature-access.repository.ts    # Access tracking
│   └── plan.repository.ts             # Plan management
├── guards/
│   └── subscription-access.guard.ts    # Access control
├── providers/
│   └── internal.provider.ts           # Payment webhooks
├── entities/
│   └── subscription.entity.ts         # Data models & DTOs
├── dtos/
│   └── subscription.dto.ts            # Request/response DTOs
└── subscription.module.ts             # Module configuration
```

## Core Components

### Entities

#### 1. FeatureEntity
Defines individual features available for subscription:

```typescript
class FeatureEntity {
  id?: string;
  featureType: FeatureTypeEnum;
  name: string;
  description: string;
  trialDurationDays: number;
  trialUsageLimit?: number;
  isActive: boolean;
}
```

#### 2. PlanEntity
Subscription plans that bundle multiple features:

```typescript
class PlanEntity {
  id?: string;
  planType: PlanTypeEnum;
  name: string;
  description: string;
  price: number; // in BDT
  durationDays: number;
  features: FeatureTypeEnum[];
  isActive: boolean;
}
```

#### 3. SubscriptionEntity
User's active subscription to a plan:

```typescript
class SubscriptionEntity {
  id?: string;
  userId: string;
  planId: string;
  planType: PlanTypeEnum;
  status: SubscriptionStatusEnum;
  startDate: Date;
  endDate: Date;
  autoRenew: boolean;
  paymentId?: string;
}
```

#### 4. FeatureAccessEntity
Tracks user access to individual features:

```typescript
class FeatureAccessEntity {
  id?: string;
  userId: string;
  featureType: FeatureTypeEnum;
  accessType: AccessTypeEnum; // Trial or Paid
  startDate: Date;
  endDate?: Date;
  usageCount: number;
  usageLimit?: number;
  subscriptionId?: string;
  isActive: boolean;
}
```

### Enums

#### FeatureTypeEnum
```typescript
enum FeatureTypeEnum {
  FoodScanner = 'FoodScanner',
  AICoach = 'AICoach',
  PremiumWorkouts = 'PremiumWorkouts',
  NutritionPlanning = 'NutritionPlanning',
  AdvancedAnalytics = 'AdvancedAnalytics'
}
```

#### PlanTypeEnum
```typescript
enum PlanTypeEnum {
  FoodScannerOnly = 'FoodScannerOnly',
  Basic = 'Basic',
  Premium = 'Premium',
  Pro = 'Pro'
}
```

#### SubscriptionStatusEnum
```typescript
enum SubscriptionStatusEnum {
  Active = 'Active',
  Expired = 'Expired',
  Cancelled = 'Cancelled',
  PendingPayment = 'PendingPayment'
}
```

## Features

### 1. Trial Management
- **Configurable trial periods** per feature (e.g., 7 days for Food Scanner)
- **Usage-based trials** with limits (e.g., 10 scans for Food Scanner)
- **Time-based trials** for unlimited features during trial period
- **One-time trial** per user per feature

### 2. Subscription Plans
- **Feature bundling** - plans can include multiple features
- **Flexible pricing** - different price points for different plan tiers
- **Auto-renewal** support for recurring subscriptions
- **Plan upgrades/downgrades** capabilities

### 3. Access Control
- **Feature gates** using `@RequireFeature()` decorator
- **Real-time access validation** with usage tracking
- **Detailed error messages** for different access scenarios
- **Trial promotion** when access is denied

### 4. Payment Integration
- **bKash payment gateway** integration
- **Webhook handling** for payment status updates
- **Automatic subscription activation** on successful payment
- **Payment failure handling** with proper status updates

### 5. Usage Analytics
- **Usage tracking** per feature per user
- **Remaining usage calculation** for limited features
- **Access history** and analytics capabilities
- **Usage summary** endpoints for dashboard

## API Endpoints

### Authentication
All endpoints require user authentication via Bearer token.

### Feature Access

#### Get Feature Access Status
```http
GET /subscription/access/{featureType}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "featureType": "FoodScanner",
  "hasAccess": true,
  "accessType": "Trial",
  "isTrialActive": true,
  "trialEndsAt": "2024-01-15T10:30:00Z",
  "usageCount": 5,
  "usageLimit": 10,
  "remainingUsage": 5
}
```

#### Start Feature Trial
```http
POST /subscription/trial/{featureType}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "message": "Trial started for FoodScanner",
  "trialAccess": {
    "userId": "user123",
    "featureType": "FoodScanner",
    "accessType": "Trial",
    "startDate": "2024-01-08T10:30:00Z",
    "endDate": "2024-01-15T10:30:00Z",
    "usageCount": 0,
    "usageLimit": 10,
    "isActive": true
  }
}
```

### Subscription Management

#### Get Available Plans
```http
GET /subscription/plans
Authorization: Bearer <token>
```

**Response:**
```json
[
  {
    "id": "plan1",
    "planType": "Premium",
    "name": "Premium Plan",
    "description": "Access to AI Coach and Premium Workouts",
    "price": 299,
    "durationDays": 30,
    "features": ["AICoach", "PremiumWorkouts"],
    "isActive": true
  }
]
```

#### Create Subscription Checkout
```http
POST /subscription/checkout
Authorization: Bearer <token>
Content-Type: application/json

{
  "planType": "Premium",
  "autoRenew": true
}
```

**Response:**
```json
{
  "message": "Checkout created successfully",
  "paymentURL": "https://checkout.bkash.com/payment/123456"
}
```

#### Get User Subscriptions
```http
GET /subscription/my-subscriptions
Authorization: Bearer <token>
```

**Response:**
```json
[
  {
    "id": "sub123",
    "userId": "user123",
    "planId": "plan1",
    "planType": "Premium",
    "status": "Active",
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-01-31T23:59:59Z",
    "autoRenew": true
  }
]
```

#### Cancel Subscription
```http
DELETE /subscription/{subscriptionId}
Authorization: Bearer <token>
```

**Response:**
```json
{
  "message": "Subscription cancelled successfully"
}
```

#### Get Usage Summary
```http
GET /subscription/usage-summary
Authorization: Bearer <token>
```

**Response:**
```json
{
  "userId": "user123",
  "features": [
    {
      "feature": "FoodScanner",
      "featureType": "FoodScanner",
      "hasAccess": true,
      "accessType": "Paid",
      "isTrialActive": false,
      "subscriptionEndsAt": "2024-01-31T23:59:59Z",
      "usageCount": 45,
      "usageLimit": null,
      "remainingUsage": null
    }
  ]
}
```

## Usage Examples

### 1. Protecting Endpoints with Feature Access

```typescript
import { RequireFeature } from '../guards/subscription-access.guard';
import { FeatureTypeEnum } from '../entities/subscription.entity';

@Controller('food-scanner')
export class FoodScannerController {
  
  @Post('analyze')
  @RequireFeature(FeatureTypeEnum.FoodScanner)
  async analyzeFood(@Body() analyzeDto: AnalyzeFoodDto) {
    // This endpoint is protected by subscription/trial access
    return await this.foodScannerService.analyze(analyzeDto);
  }
}
```

### 2. Checking Access Programmatically

```typescript
@Injectable()
export class SomeService {
  constructor(
    private subscriptionService: SubscriptionService
  ) {}

  async performPremiumAction(userId: string) {
    const accessStatus = await this.subscriptionService.checkFeatureAccess(
      userId, 
      FeatureTypeEnum.PremiumWorkouts
    );

    if (!accessStatus.hasAccess) {
      throw new ForbiddenException('Premium subscription required');
    }

    // Proceed with premium action
    return await this.executePremiumFeature();
  }
}
```

### 3. Starting Trials

```typescript
// In your service
async startFoodScannerTrial(userId: string) {
  try {
    const trialAccess = await this.subscriptionService.startTrial(
      userId, 
      FeatureTypeEnum.FoodScanner
    );
    
    // Notify user about trial start
    await this.notificationService.sendTrialStartedNotification(userId, trialAccess);
    
    return trialAccess;
  } catch (error) {
    if (error.message.includes('already been used')) {
      // Handle case where user already used trial
      throw new BadRequestException('Trial already used for this feature');
    }
    throw error;
  }
}
```

### 4. Handling Payment Webhooks

```typescript
// In your webhook handler
@Post('bkash-webhook')
async handleBkashWebhook(@Body() webhookData: BkashWebhookDto) {
  if (webhookData.moduleType === ModuleTypeEnum.Subscription) {
    if (webhookData.status === 'success') {
      await this.subscriptionProviderForInternal.onBkashPaymentSuccessful(
        webhookData.referenceId
      );
    } else if (webhookData.status === 'failed') {
      await this.subscriptionProviderForInternal.onBkashPaymentFailed(
        webhookData.referenceId
      );
    }
  }
}
```

## Integration Patterns

### 1. Module Integration

To use the subscription system in your module:

```typescript
import { SubscriptionModule } from '../subscription/subscription.module';

@Module({
  imports: [
    SubscriptionModule, // Import subscription module
    // other imports
  ],
  controllers: [YourController],
  providers: [YourService],
})
export class YourModule {}
```

### 2. Guard Integration

Protect endpoints with feature requirements:

```typescript
import { SubscriptionAccessGuard, RequireFeature } from '../subscription/guards/subscription-access.guard';

@Controller('premium-feature')
@UseGuards(OnlyAuthGuard, SubscriptionAccessGuard)
export class PremiumFeatureController {
  
  @Get('data')
  @RequireFeature(FeatureTypeEnum.AdvancedAnalytics)
  async getPremiumData() {
    // Protected endpoint
  }
}
```

### 3. Service Integration

Use subscription service in your business logic:

```typescript
@Injectable()
export class YourService {
  constructor(
    private subscriptionService: SubscriptionService
  ) {}

  async yourMethod(userId: string) {
    // Check access before performing action
    const hasAccess = await this.subscriptionService.recordUsageAndCheckAccess(
      userId, 
      FeatureTypeEnum.AICoach
    );

    if (!hasAccess) {
      throw new ForbiddenException('AI Coach subscription required');
    }

    // Perform the action
  }
}
```

### 4. Provider Integration

Handle payment events:

```typescript
@Injectable()
export class YourPaymentHandler {
  constructor(
    private subscriptionProvider: SubscriptionProviderForInternal
  ) {}

  async handleSuccessfulPayment(referenceId: string, moduleType: ModuleTypeEnum) {
    if (moduleType === ModuleTypeEnum.Subscription) {
      await this.subscriptionProvider.onBkashPaymentSuccessful(referenceId);
    }
  }
}
```

## Configuration

### Environment Variables

```env
# bKash Configuration (inherited from existing bKash module)
BKASH_APP_KEY=your_bkash_app_key
BKASH_APP_SECRET=your_bkash_app_secret
BKASH_USERNAME=your_bkash_username
BKASH_PASSWORD=your_bkash_password
BKASH_BASE_URL=https://tokenized.pay.bka.sh/v1.2.0-beta

# Database Configuration (MongoDB)
MONGODB_URI=mongodb://localhost:27017/fitsomnia

# Feature Configuration (can be moved to database)
FOOD_SCANNER_TRIAL_DAYS=7
FOOD_SCANNER_TRIAL_LIMIT=10
AI_COACH_TRIAL_DAYS=3
AI_COACH_TRIAL_LIMIT=5
```

### Feature Configuration

Current feature configurations (hardcoded in service, can be moved to database):

```typescript
const featureConfigs = {
  [FeatureTypeEnum.FoodScanner]: {
    trialDurationDays: 7,
    trialUsageLimit: 10,
  },
  [FeatureTypeEnum.AICoach]: {
    trialDurationDays: 3,
    trialUsageLimit: 5,
  },
  [FeatureTypeEnum.PremiumWorkouts]: {
    trialDurationDays: 7,
    trialUsageLimit: undefined, // Unlimited during trial
  },
  [FeatureTypeEnum.NutritionPlanning]: {
    trialDurationDays: 5,
    trialUsageLimit: undefined,
  },
  [FeatureTypeEnum.AdvancedAnalytics]: {
    trialDurationDays: 7,
    trialUsageLimit: undefined,
  },
};
```

### Plan Configuration

Sample plan configurations (should be stored in database):

```typescript
const plans = [
  {
    planType: PlanTypeEnum.FoodScannerOnly,
    name: 'Food Scanner Only',
    description: 'Access to food scanning feature',
    price: 99, // BDT
    durationDays: 30,
    features: [FeatureTypeEnum.FoodScanner],
  },
  {
    planType: PlanTypeEnum.Basic,
    name: 'Basic Plan',
    description: 'Food Scanner + Basic Workouts',
    price: 199,
    durationDays: 30,
    features: [FeatureTypeEnum.FoodScanner, FeatureTypeEnum.PremiumWorkouts],
  },
  {
    planType: PlanTypeEnum.Premium,
    name: 'Premium Plan',
    description: 'All features except Advanced Analytics',
    price: 299,
    durationDays: 30,
    features: [
      FeatureTypeEnum.FoodScanner,
      FeatureTypeEnum.AICoach,
      FeatureTypeEnum.PremiumWorkouts,
      FeatureTypeEnum.NutritionPlanning,
    ],
  },
  {
    planType: PlanTypeEnum.Pro,
    name: 'Pro Plan',
    description: 'All features included',
    price: 399,
    durationDays: 30,
    features: Object.values(FeatureTypeEnum),
  },
];
```

## Security

### Access Control
- **Authentication required** for all endpoints
- **User isolation** - users can only access their own subscriptions
- **Role-based access** - only users (not admins) can create subscriptions
- **Feature-level protection** via guards

### Payment Security
- **Secure webhook handling** for payment status updates
- **Reference ID validation** to prevent unauthorized activations
- **Payment status verification** before subscription activation
- **No sensitive payment data storage** - delegated to bKash

### Data Validation
- **Input validation** using DTOs and decorators
- **Enum validation** for feature types and plan types
- **Date validation** for subscription periods
- **User ID validation** from authenticated context

## Testing

### Unit Tests
```typescript
describe('SubscriptionService', () => {
  let service: SubscriptionService;
  let mockSubscriptionRepository: jest.Mocked<SubscriptionRepository>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        SubscriptionService,
        {
          provide: SubscriptionRepository,
          useValue: createMockRepository(),
        },
        // other mocks
      ],
    }).compile();

    service = module.get<SubscriptionService>(SubscriptionService);
  });

  it('should check feature access correctly', async () => {
    // Test implementation
  });

  it('should start trial for new users', async () => {
    // Test implementation
  });

  it('should prevent duplicate trials', async () => {
    // Test implementation
  });
});
```

### Integration Tests
```typescript
describe('Subscription E2E', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  it('/subscription/trial/FoodScanner (POST)', () => {
    return request(app.getHttpServer())
      .post('/subscription/trial/FoodScanner')
      .set('Authorization', 'Bearer ' + validToken)
      .expect(201)
      .expect((res) => {
        expect(res.body.message).toContain('Trial started');
      });
  });
});
```

### Load Testing
Consider testing the subscription system under load for:
- **Concurrent trial starts** for the same user
- **High volume of access checks** during peak usage
- **Payment webhook handling** under load
- **Database performance** with large user bases

## Best Practices

1. **Always check access** before performing premium operations
2. **Use guards** for endpoint protection rather than manual checks
3. **Handle payment webhooks** idempotently to prevent duplicate processing
4. **Monitor trial usage** to detect abuse patterns
5. **Implement proper logging** for audit trails
6. **Use transactions** for critical operations like subscription activation
7. **Validate user context** in all operations to prevent access to other users' data
8. **Cache access status** for high-frequency operations to reduce database load

## Future Enhancements

1. **Subscription analytics dashboard** for admin users
2. **Automated trial reminder notifications**
3. **Subscription upgrade/downgrade workflows**
4. **Family/team subscription plans**
5. **Promo codes and discounts**
6. **Subscription pause/resume functionality**
7. **Multi-currency support**
8. **Subscription gift functionality**

---

This documentation covers the complete Fitsomnia subscription system implementation. For specific implementation details, refer to the source code in `/src/modules/subscription/`.
